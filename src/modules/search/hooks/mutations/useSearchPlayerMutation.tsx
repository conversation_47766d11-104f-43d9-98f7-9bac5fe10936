import { gql, useMutation } from '@apollo/client';
import { useCallback, useRef } from 'react';
import { GAME_MODES, GAME_TYPES } from 'modules/game/constants/game';
import { getGameConfigInputForGame } from 'modules/game/utils/getGameCategoryAndGameTypeInfos';
import { handleAsync } from 'core/utils/asyncUtils';

const START_SEARCHING_MUTATION = gql`
  mutation StartSearching($gameConfig: GameConfigInput) {
    startSearching(gameConfig: $gameConfig)
  }
`;

const useSearchPlayerMutation = () => {
  const [startSearchingMutation, { data, loading, error }] = useMutation(
    START_SEARCHING_MUTATION,
  );
  const abortControllerRef = useRef<AbortController | null>(null);

  const startSearching = useCallback(
    async ({
      numPlayers = 2,
      timeLimit,
      gameType = GAME_TYPES.DMAS,
    } = EMPTY_OBJECT) => {
      const variables = {
        gameConfig: getGameConfigInputForGame({
          gameType,
          gameMode: GAME_MODES.ONLINE_SEARCH,
          timeLimit,
          configs: {
            numPlayers,
          },
        }),
      };
      if (abortControllerRef.current) {
        abortControllerRef.current?.abort();
      }
      abortControllerRef.current = new AbortController();
      return handleAsync(startSearchingMutation, {
        variables,
        context: {
          fetchOptions: {
            signal: abortControllerRef.current.signal,
          },
        },
      });
    },
    [startSearchingMutation],
  );

  return {
    searchResult: data?.startSearching,
    startSearching,
    loading,
    error,
  };
};

export default useSearchPlayerMutation;
