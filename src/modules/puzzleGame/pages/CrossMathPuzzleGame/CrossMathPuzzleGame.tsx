import React, { useEffect } from 'react';
import _size from 'lodash/size';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import { Redirect } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';
import Loading from 'atoms/Loading';
import { PuzzleGame } from 'modules/puzzleGame/types/puzzleGame';
import useJoinPuzzleGame from 'modules/puzzleGame/hooks/mutations/useJoinPuzzleGame';
import WaitingForFriend from 'modules/puzzleGame/components/WaitingForFriend';
import usePuzzleGameOwner from 'modules/puzzleGame/hooks/usePuzzleGameOwner';
import PlayCrossMathPuzzleGameContainer from 'modules/puzzleGame/pages/CrossMathPuzzleGame/PlayCrossMathPuzzleGameContainer';
import ErrorView from 'atoms/ErrorView';
import _isEmpty from 'lodash/isEmpty';
import { PUZZLE_GAME_STATUS } from '../../constants/puzzleGame';
import PuzzleGameFullPage from '../../components/PuzzleGameFullPage';
import PlayWithFriendReadyToStart from '../../components/ReadyToStart';
import puzzleGameReader from '@/src/core/readers/puzzleGameReader';

const COMPONENT_FACTORY = {
  [PUZZLE_GAME_STATUS.CREATED]: WaitingForFriend,
  [PUZZLE_GAME_STATUS.READY]: PlayWithFriendReadyToStart,
  [PUZZLE_GAME_STATUS.STARTED]: PlayCrossMathPuzzleGameContainer,
};

const JOINED_GAME_CALLED: any = {};

const CrossMathPuzzleGameContainer = ({ game }: { game: PuzzleGame }) => {
  const { user } = useSession();
  const { joinPuzzleGame } = useJoinPuzzleGame();

  const { players, gameStatus, _id: gameId, config } = game ?? EMPTY_OBJECT;

  const { checkIsGameOwner } = usePuzzleGameOwner();

  const isGameOwner = checkIsGameOwner({ game });

  const isUserInTheGame = _includes(
    _map(players, 'userId'),
    userReader.id(user),
  );

  const numPlayers = puzzleGameReader.numPlayers(game);

  useEffect(() => {
    if (JOINED_GAME_CALLED[gameId]) return;

    JOINED_GAME_CALLED[gameId] = true;

    const shouldJoinTheGame =
      gameStatus === PUZZLE_GAME_STATUS.CREATED &&
      _size(players) === 1 &&
      !isGameOwner;
    if (shouldJoinTheGame) {
      joinPuzzleGame({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameStatus, players, isGameOwner, gameId, joinPuzzleGame]);

  if (_isEmpty(game)) {
    return <Loading label="Loading Puzzle Game" />;
  }

  if (_size(players) === numPlayers && !isUserInTheGame) {
    return <PuzzleGameFullPage />;
  }

  if (gameStatus === PUZZLE_GAME_STATUS.ENDED) {
    return <Redirect href={`puzzle-game/result/${gameId}`} />;
  }

  if (gameStatus === PUZZLE_GAME_STATUS.CREATED && _size(players) === 1) {
    if (isGameOwner) {
      return <WaitingForFriend game={game} />;
    }
    return <Loading label="Joining the Game..." />;
  }

  const Component = COMPONENT_FACTORY[gameStatus];

  if (!Component) {
    return <ErrorView errorMessage={`Invalid game status ${gameStatus}`} />;
  }

  return <Component game={game} />;
};

export default React.memo(CrossMathPuzzleGameContainer);
