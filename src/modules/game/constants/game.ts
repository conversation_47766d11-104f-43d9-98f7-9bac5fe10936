export const GAME_STATUS = {
  CREATED: 'CREATED',
  READY: 'READY',
  STARTED: 'STARTED',
  PAUSED: 'PAUSED',
  ENDED: 'ENDED',
  CANCELLED: 'CANCELLED',
};

export const GAME_STATUS_LABEL = {
  [GAME_STATUS.CREATED]: 'created',
  [GAME_STATUS.READY]: 'ready',
  [GAME_STATUS.STARTED]: 'started',
  [GAME_STATUS.PAUSED]: 'paused',
  [GAME_STATUS.ENDED]: 'ended',
  [GAME_STATUS.CANCELLED]: 'cancelled',
};

export const GAME_EVENTS = {
  USER_JOINED: 'USER_JOINED',
  GAME_STARTED: 'GAME_STARTED',
  CORRECT_MOVE_MADE: 'CORRECT_MOVE_MADE',
  INCORRECT_MOVE_MADE: 'INCORRECT_MOVE_MADE',
  GAME_ENDED: 'GAME_ENDED',
  PLAYER_REMOVED: 'PLAYER_REMOVED',
};

export const GAME_RESULT_STATUS = {
  WIN: 'WIN',
  LOSS: 'LOSS',
};

export const EACH_QUESTION_RESULT_TIME = 5;

export const GAME_CATEGORY = {
  BLITZ: 'BLITZ',
  CLASSICAL: 'CLASSICAL',
  MEMORY: 'MEMORY',
  PUZZLE: 'PUZZLE',
};

export const GAME_TYPES = {
  DMAS: 'DMAS',
  FASTEST_FINGER: 'FASTEST_FINGER',
  DMAS_TIME_BANK: 'DMAS_TIME_BANK',
  DMAS_ABILITY: 'DMAS_ABILITY',
  FLASH_ANZAN: 'FLASH_ANZAN',
  CROSS_MATH_PUZZLE: 'CROSS_MATH_PUZZLE',
  KEN_KEN_PUZZLE: 'KEN_KEN_PUZZLE',

  PLAY_ONLINE: 'PLAY_ONLINE',
  ABILITY_DUELS: 'ABILITY_DUELS',
  GROUP_PLAY: 'GROUP_PLAY',
  PLAY_WITH_FRIEND: 'PLAY_WITH_FRIEND',
};

export const GAME_MODES = {
  ONLINE_SEARCH: 'ONLINE_SEARCH',
  ONLINE_CHALLENGE: 'ONLINE_CHALLENGE',
  GROUP_PLAY: 'GROUP_PLAY',
  PRACTICE: 'PRACTICE',
  RUSH_WITH_TIME: 'RUSH_WITH_TIME',
  RUSH_WITHOUT_TIME: 'RUSH_WITHOUT_TIME',
  PLAY_VIA_LINK: 'PLAY_VIA_LINK',
  SUMDAY_SHOWDOWN: 'SUMDAY_SHOWDOWN',
  SURVIVAL_SATURDAY: 'SURVIVAL_SATURDAY',
};

interface DefaultGameConfigInput {
  timeLimit: number;
}

interface GroupPlayGameConfigInput {
  maxTimePerQuestion: number;
  difficultyLevel: number[];
  maxGapBwGame: number;
  maxPlayers: number;
  minPlayers: number;
  questionTags: string[];
}

interface ShowdownGameConfigInput {
  numberOfGames: number;
  // In Future might be showdown config will be sent from client side
}

interface DefaultGameModeConfigInput {
  numPlayers: number;
}

interface PuzzleGameConfigInput {
  timeLimit: number;
  numOfQuestions: number;
}

interface CategorySpecificGameConfigInput {
  category: string;
  blitz?: DefaultGameConfigInput;
  classical?: DefaultGameConfigInput;
  memory?: DefaultGameConfigInput;
  puzzle?: PuzzleGameConfigInput;
}

interface GameTypeSpecificConfigInput {
  type: string;
  // In Future might be type specific config will come
}

interface GameModeSpecificConfigInput {
  mode: string;
  onlineSearch?: DefaultGameModeConfigInput;
  onlineChallenge?: DefaultGameModeConfigInput;
  groupPlay?: GroupPlayGameConfigInput;
  practice?: DefaultGameModeConfigInput;
  rushWithTime?: DefaultGameModeConfigInput;
  rushWithoutTime?: DefaultGameModeConfigInput;
  playViaLink?: DefaultGameModeConfigInput;
  sumdayShowdown?: ShowdownGameConfigInput;
  survivalSaturday?: DefaultGameModeConfigInput;
}

export interface GameConfigInput {
  categorySpecificGameConfig: CategorySpecificGameConfigInput;
  gameTypeSpecificConfig: GameTypeSpecificConfigInput;
  modeSpecificConfig: GameModeSpecificConfigInput;
}

interface FlashAnzanAnswerInput {
  maxScore: number;
}

interface GameTypeSpecificAnswerInput {
  type: string;
  flashAnzan?: FlashAnzanAnswerInput;
}

export interface SubmitAnswerInput {
  gameId: string;
  questionId: string;
  submittedValue: string;
  isCorrect: boolean;
  inCorrectAttempts: number;
  timeOfSubmission: number;
  gameTypeSpecificAnswer: GameTypeSpecificAnswerInput;
}
