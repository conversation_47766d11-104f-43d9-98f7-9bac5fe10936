import React, { useMemo, useState } from 'react';
import { Image, View } from 'react-native';
import { Text } from '@rneui/themed';
import vsImage from '@/assets/images/LinearGradientIcons/vs.png';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from '@/src/core/constants/themes/dark';
import _toNumber from 'lodash/toNumber';
import { GAME_TYPES } from 'modules/home/<USER>/gameTypes';
import { useSession } from 'modules/auth/containers/AuthProvider';
import styles from './GameLobbyPlayerCards.style';
import useGameContext from '../../hooks/useGameContext';
import Card from '../usercard/UserCard';
import gameReader from 'core/readers/gameReader';

const GameLobbyPlayerCards = ({ timeLimit }) => {
  const { players, game } = useGameContext();

  const { gameType } = game ?? EMPTY_OBJECT;

  const isFlashAnzan = useMemo(
    () => gameType === GAME_TYPES.FLASH_ANZAN,
    [gameType]
  );

  if (!timeLimit) {
    timeLimit = gameReader.timeLimit(game);
  }
  const { user } = useSession();

  const [cardDimensions, setCardDimensions] = useState({
    width: 0,
    height: 0
  });

  const handleLayout = (event) => {
    const { width: parentWidth } = event.nativeEvent.layout;
    const cardWidth = Math.min(Math.max(parentWidth * 0.32, 90), 100);
    const cardHeight = cardWidth * 1.1;
    setCardDimensions({ width: cardWidth, height: cardHeight });
  };

  return (
    <View style={ styles.cont }>
      <View style={ styles.container } onLayout={ handleLayout }>
        <View
          style={ { width: cardDimensions.width, height: cardDimensions.height } }
        >
          <Card
            user={ players ? players[0] : user }
            key={ players ? players[0]?._id : user._id }
            gameType={ gameType }
          />
        </View>
        <View style={ styles.vsContainer }>
          <Image source={ vsImage } style={ styles.vsImage } />
          { isFlashAnzan ? (
            <Text style={ styles.gameTime }>{ ' 3 Ques' }</Text>
          ) : (
            <View style={ { flexDirection: 'row' } }>
              <MaterialIcons
                name="timer"
                color={ Dark.colors.textDark }
                size={ 20 }
              />
              <Text style={ styles.gameTime }>
                { ' ' }
                { _toNumber(timeLimit) / 60 }:00
              </Text>
            </View>
          ) }
        </View>
        <View
          style={ { width: cardDimensions.width, height: cardDimensions.height } }
        >
          <Card
            user={ players?.[1] }
            key={ players?.[1]?._id }
            gameType={ gameType }
          />
        </View>
      </View>
    </View>
  );
};

export default React.memo(GameLobbyPlayerCards);
