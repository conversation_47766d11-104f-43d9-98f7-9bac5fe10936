import React from 'react';
import gameReader from 'core/readers/gameReader';
import { GAME_STATUS } from '../../constants/game';
import PlayWithFriendReadyToStart from '../../pages/PlayWithFriend/components/ReadyToStart/ReadyToStart';

const ReadyToStartContainer = (props) => {
  const { game } = props;
  const gameStatus = gameReader.gameStatus(game);

  if (gameStatus !== GAME_STATUS.READY) {
    return null;
  }

  return <PlayWithFriendReadyToStart {...props} />;
};

export default React.memo(ReadyToStartContainer);
