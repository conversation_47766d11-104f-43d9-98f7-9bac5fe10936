import gameReader from 'core/readers/gameReader';
import {
  GAME_MODES,
  GAME_STATUS,
  GAME_TYPES,
} from 'modules/game/constants/game';
import WaitingForFriend from 'modules/game/pages/PlayWithFriend/components/WaitingForFriend';
import ReadyToStart from 'modules/game/pages/PlayWithFriend/components/ReadyToStart';
import PlayGame from 'modules/game/pages/PlayGame/PlayGame';
import GroupPlayWaitingPage from 'modules/game/pages/GroupPlayWaitingPage';
import GroupGamePlay from 'modules/game/pages/GroupGamePlay';
import PlayFlashAnzanDuelGame from 'modules/game/pages/FlashAnzan/Components/PlayFlashAnzanDuelGame';
import FastestFingerPlayGame from 'modules/game/pages/FastestFinger/FastestFingerPlayGame';

export const getDMASComponentFactoryForGame = (game: any) => {
  const gameMode = gameReader.gameMode(game);
  switch (gameMode) {
    case GAME_MODES.GROUP_PLAY:
      return {
        [GAME_STATUS.CREATED]: GroupPlayWaitingPage,
        [GAME_STATUS.READY]: GroupPlayWaitingPage,
        [GAME_STATUS.STARTED]: GroupGamePlay,
      };
    default:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayGame,
      };
  }
};

export const getComponentFactoryForGame = (game: any) => {
  const gameType = gameReader.gameType(game);

  switch (gameType) {
    case GAME_TYPES.DMAS:
      return getDMASComponentFactoryForGame(game);
    case GAME_TYPES.FASTEST_FINGER:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: FastestFingerPlayGame,
      };
    case GAME_TYPES.DMAS_ABILITY || GAME_TYPES.ABILITY_DUELS:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayGame,
      };
    case GAME_TYPES.FLASH_ANZAN:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayFlashAnzanDuelGame,
      };
    case GAME_TYPES.CROSS_MATH_PUZZLE:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayGame,
      };
    case GAME_TYPES.KEN_KEN_PUZZLE:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayGame,
      };
    case GAME_TYPES.DMAS_TIME_BANK:
      return {
        [GAME_STATUS.CREATED]: WaitingForFriend,
        [GAME_STATUS.READY]: ReadyToStart,
        [GAME_STATUS.STARTED]: PlayGame,
      };
    default:
      return null;
  }
};
