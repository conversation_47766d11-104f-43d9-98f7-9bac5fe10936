import PropTypes from 'prop-types';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Animated, View } from 'react-native';
import { But<PERSON>, Text } from '@rneui/themed';
import DarkTheme from '@/src/core/constants/themes/dark';
import { getSiteUrl } from 'core/constants/appConstants';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import useNativeUrlSharing from 'core/hooks/useNativeUrlSharing';
import DotAnimation from 'shared/DotAnimation';
import gameReader from 'core/readers/gameReader';
import { GAME_MODES } from 'modules/game/constants/game';
import GroupPlayWaitingPage from 'modules/game/pages/GroupPlayWaitingPage';
import useHandleLeaveGame from '../../../../hooks/useHandleLeaveGame';
import styles from './WaitingForFriend.styles';

const TRACKED_EVENT_FOR_GAMES = {};

const WaitingForFriend = (props) => {
  const { game } = props ?? EMPTY_OBJECT;

  const { _id: gameId } = game;

  const siteUrl = useMemo(() => getSiteUrl(), []);

  const rotateValue = useRef(new Animated.Value(0)).current;

  const GAME_URL = `${siteUrl}/game/${gameId}/play`;

  const { handleShare } = useNativeUrlSharing({ url: GAME_URL });

  const onPressShareLink = useCallback(async () => {
    Analytics.track(ANALYTICS_EVENTS.PLAY_WITH_FRIEND?.CLICKED_ON_COPY_LINK, {
      [PAGE_NAME_KEY]: PAGE_NAMES.PLAY_WITH_FRIEND_WAITING_PAGE,
    });
    await handleShare();
  }, [handleShare]);

  useEffect(() => {
    const rotateAnimation = Animated.loop(
      Animated.timing(rotateValue, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
      {
        iterations: 1,
      },
    );
    rotateAnimation.start();

    return () => rotateAnimation.stop();
  }, []);

  useEffect(() => {
    if (gameId && !TRACKED_EVENT_FOR_GAMES[gameId]) {
      Analytics.track(ANALYTICS_EVENTS.PLAY_WITH_FRIEND?.VIEW_PWF_WAITING_PAGE);
      TRACKED_EVENT_FOR_GAMES[gameId] = true;
    }
  }, [gameId]);

  const rotate = rotateValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const { onPressLeaveGame, isCancellingGame } = useHandleLeaveGame({ gameId });
  const gameMode = gameReader.gameMode(game);

  if (gameMode === GAME_MODES.GROUP_PLAY) {
    return <GroupPlayWaitingPage game={game} />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.container}>
        <Animated.Text style={[styles.timer, { transform: [{ rotate }] }]}>
          ⏳
        </Animated.Text>
        <Text style={styles.waitingText}>
          Waiting for your Friend to join
          <DotAnimation />
        </Text>
        <Text style={styles.shareLinkLabel}>
          Share the link with your friend.
        </Text>
        <Button
          title={GAME_URL}
          onPress={onPressShareLink}
          titleProps={{ numberOfLines: 1 }}
          icon={{
            name: 'copy',
            type: 'font-awesome-5',
            size: 16,
            color: DarkTheme.colors.secondary,
          }}
          iconRight
          iconContainerStyle={{ marginLeft: 10 }}
          titleStyle={styles.linkText}
          buttonStyle={styles.linkButton}
          containerStyle={styles.linkButtonContainer}
        />
      </View>
      <View style={{ marginBottom: 16 }}>
        <Button type="clear" onPress={onPressLeaveGame}>
          <Text style={styles.cancel}>Cancel</Text>
        </Button>
      </View>
    </View>
  );
};

WaitingForFriend.propTypes = {
  game: PropTypes.object.isRequired,
};

export default React.memo(WaitingForFriend);
