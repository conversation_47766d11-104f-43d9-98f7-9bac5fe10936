import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import _isEqual from 'lodash/isEqual';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import dark from 'core/constants/themes/dark';
import Ionicons from '@expo/vector-icons/Ionicons';
import useMediaQuery from 'core/hooks/useMediaQuery';
import UserImage from 'atoms/UserImage';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import Header from 'shared/Header';
import { Redirect } from 'expo-router';
import gameReader from 'core/readers/gameReader';
import { GAME_MODES } from 'modules/game/constants/game';
import useGameContext from '../../../hooks/useGameContext';
import styles from './GroupPlayChatRoom.style';
import { useSession } from '../../../../auth/containers/AuthProvider';
import { useGroupPlayChatContext } from '../../../hooks/useGroupPlayChatContext';

const GroupPlayChatRoomPage = ({ gameId }) => {
  const { messages, sendMessage } = useGroupPlayChatContext();
  const { players, game } = useGameContext();

  const gameType = useMemo(() => _get(game, 'gameType'), [game]);

  const currentTime = new Date(getCurrentTimeWithOffset());
  const endTimeDate = new Date(game?.startTime);
  const timeDifference = (currentTime - endTimeDate) / (1000 * 60 * 60);

  const [newMessage, setNewMessage] = useState('');
  const { userId: currentUserId } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const flatListRef = useRef(null);
  const messageTextRef = useRef(null);

  const getPlayer = useCallback(
    ({ userId }) => _find(players, (player) => player?._id == userId),
    [players],
  );

  const handleSendMessage = useCallback(() => {
    if (newMessage.trim()) {
      sendMessage({ message: newMessage.trim() });
      setNewMessage('');
      messageTextRef?.current?.focus();
    }
  }, [sendMessage, newMessage]);

  const handleKeyPress = useCallback(
    (e) => {
      if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        handleSendMessage();
        e.preventDefault();
      }
    },
    [handleSendMessage],
  );

  const renderMessage = useCallback(
    ({ item }) => {
      const { userId, message, userName } = item ?? {};
      const playerInfo = getPlayer({ userId });
      const isOwnMessage = _isEqual(currentUserId, userId);
      return (
        <View style={styles.messageContainer}>
          <View style={{ flexDirection: 'row', gap: 3 }}>
            <UserImage user={playerInfo ?? { profileImageUrl: '' }} size={25} />
            <Text
              style={[
                styles.sender,
                isOwnMessage && { color: dark.colors.messageUserNameText },
              ]}
            >
              {isOwnMessage ? 'you' : userName}
            </Text>
          </View>
          <Text style={styles.message}>{message}</Text>
        </View>
      );
    },
    [currentUserId, getPlayer],
  );

  const gameMode = gameReader.gameMode(game);

  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: false });
    }
  }, [messages]);

  if (!isCompactMode) {
    return <Redirect href={`/game/${gameId}/result`} />;
  }

  if (gameMode !== GAME_MODES.GROUP_PLAY || timeDifference >= 24) {
    return null;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 24}
    >
      <View style={{ flex: 1, width: '100%', height: '100%' }}>
        <Header title="Chat Room" />
        <View style={{ paddingHorizontal: 16, flex: 1 }}>
          <Text
            style={[styles.header, { textAlign: 'center', color: 'white' }]}
          >
            Note : Chat Room Will disappear after 24hr of game start
          </Text>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            showsHorizontalScrollIndicator={false}
            style={styles.chatList}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => index.toString()}
          />
          <View style={[styles.inputContainer, { paddingBottom: 15 }]}>
            <TextInput
              ref={messageTextRef}
              style={styles.input}
              placeholder="Message here"
              placeholderTextColor={dark.colors.textDark}
              value={newMessage}
              onChangeText={setNewMessage}
              onSubmitEditing={handleSendMessage}
              blurOnSubmit={false}
              onKeyPress={handleKeyPress}
              multiline
              returnKeyType="send"
            />
            <TouchableOpacity
              onPress={handleSendMessage}
              style={styles.sendButton}
            >
              <Ionicons
                name="send-sharp"
                size={15}
                color={dark.colors.background}
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const GroupPlayChatRoomPageContainer = React.memo(({ gameId }) => {
  if (_isEmpty(gameId) || _isNil(gameId)) {
    return null;
  }

  return <GroupPlayChatRoomPage gameId={gameId} />;
});

export default React.memo(GroupPlayChatRoomPageContainer);
