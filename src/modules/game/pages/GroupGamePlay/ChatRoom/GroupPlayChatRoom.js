import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import _isEqual from 'lodash/isEqual';
import _find from 'lodash/find';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import _get from 'lodash/get';
import Ionicons from '@expo/vector-icons/Ionicons';
import dark from 'core/constants/themes/dark';
import useMediaQuery from 'core/hooks/useMediaQuery';
import gameReader from 'core/readers/gameReader';
import { GAME_MODES } from 'modules/game/constants/game';
import { useGroupPlayChatContext } from '../../../hooks/useGroupPlayChatContext';
import { useSession } from '../../../../auth/containers/AuthProvider';
import styles from './GroupPlayChatRoom.style';
import useGameContext from '../../../hooks/useGameContext';
import UserImage from '../../../../../components/atoms/UserImage';
import getCurrentTimeWithOffset from '../../../../../core/utils/getCurrentTimeWithOffset';

const GroupPlayChatRoom = ({ gameId }) => {
  const { messages, sendMessage } = useGroupPlayChatContext();
  const { players, game } = useGameContext();

  const gameType = useMemo(() => _get(game, 'gameType'), [game]);

  const currentTime = new Date(getCurrentTimeWithOffset());
  const endTimeDate = new Date(game?.startTime);
  const timeDifference = (currentTime - endTimeDate) / (1000 * 60 * 60);

  const [newMessage, setNewMessage] = useState('');
  const { userId: currentUserId } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();
  const flatListRef = useRef(null);
  const messageTextRef = useRef(null);

  const getPlayer = useCallback(
    ({ userId }) => _find(players, (player) => player?._id == userId),
    [players],
  );

  const handleSendMessage = useCallback(() => {
    if (newMessage.trim()) {
      sendMessage({ message: newMessage.trim() });
      setNewMessage('');
      messageTextRef?.current?.focus();
    }
  }, [sendMessage, newMessage]);

  const handleKeyPress = useCallback(
    (e) => {
      if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
        handleSendMessage();
        e.preventDefault();
      }
    },
    [handleSendMessage],
  );

  const renderMessage = useCallback(
    ({ item }) => {
      const { userId, message, userName } = item ?? {};
      const playerInfo = getPlayer({ userId });
      const isOwnMessage = _isEqual(currentUserId, userId);
      return (
        <View style={styles.messageContainer}>
          <View style={{ flexDirection: 'row', gap: 3 }}>
            <UserImage user={playerInfo ?? { profileImageUrl: '' }} size={25} />
            <Text
              style={[
                styles.sender,
                isOwnMessage && { color: dark.colors.messageUserNameText },
              ]}
            >
              {isOwnMessage ? 'you' : userName}
            </Text>
          </View>
          <Text style={styles.message}>{message}</Text>
        </View>
      );
    },
    [currentUserId, getPlayer],
  );

  useEffect(() => {
    if (flatListRef.current && messages.length > 0) {
      flatListRef.current.scrollToEnd({ animated: false });
    }
  }, [messages]);

  const gameMode = gameReader.gameMode(game);
  if (
    isCompactMode ||
    gameMode !== GAME_MODES.GROUP_PLAY ||
    timeDifference >= 24
  ) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.header}>CHAT ROOM</Text>
      <Text style={[styles.header, { textAlign: 'center', color: 'white' }]}>
        Note : Chat Room Will disappear after 24hr of game start
      </Text>
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        style={styles.chatList}
        showsVerticalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
      />
      <View style={styles.inputContainer}>
        <TextInput
          ref={messageTextRef}
          style={styles.input}
          placeholder="Message here"
          placeholderTextColor={dark.colors.textDark}
          value={newMessage}
          onChangeText={setNewMessage}
          onSubmitEditing={handleSendMessage}
          blurOnSubmit={false}
          multiline
          onKeyPress={handleKeyPress}
          returnKeyType="send"
        />
        <TouchableOpacity onPress={handleSendMessage} style={styles.sendButton}>
          <Ionicons
            name="send-sharp"
            size={15}
            color={dark.colors.background}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const GroupPlayChatRoomContainer = React.memo(({ gameId }) => {
  if (_isEmpty(gameId) || _isNil(gameId)) {
    return null;
  }

  return <GroupPlayChatRoom gameId={gameId} />;
});

export default React.memo(GroupPlayChatRoomContainer);
