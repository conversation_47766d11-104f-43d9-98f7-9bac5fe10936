import React, { useEffect, useRef, useState } from 'react'
import _toNumber from 'lodash/toNumber'
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset'
import { Redirect } from 'expo-router'
import { GAME_STATUS } from '../../constants/game'
import ErrorView from 'atoms/ErrorView'
import useGameContext from '../../hooks/useGameContext'
import Loading from 'atoms/Loading'
import GroupGamePlay from './GroupGamePlay'
import gameReader from '@/src/core/readers/gameReader'

const MAX_WAITING_TIME = 5000 // 5 seconds

const GroupGamePlayContainer = ({ game }) => {
  const { reFetchGame } = useGameContext()
  const { startTime, config, _id: gameId, gameStatus } = game
  const startTimeDate = new Date(startTime)
  const timeLimit = gameReader.timeLimit(game)
  const currentTime = getCurrentTimeWithOffset()
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000

  const fetchGameTimeoutRef = useRef()

  const [gameEndedForUser, setGameEndedForUser] = useState(
    currentTime > endTime
  )

  useEffect(() => {
    const timeToGameEnd = endTime - getCurrentTimeWithOffset()

    const timeToReFetchGame = timeToGameEnd + MAX_WAITING_TIME

    if (endTime > currentTime) {
      setTimeout(() => setGameEndedForUser(true), timeToGameEnd)

      if (fetchGameTimeoutRef.current) {
        clearTimeout(fetchGameTimeoutRef.current)
      }
      // This is just for safe check, if user miss the game end event.
      fetchGameTimeoutRef.current = setTimeout(
        () => reFetchGame(),
        timeToReFetchGame
      )
    }

    return () => clearTimeout(fetchGameTimeoutRef.current)
  }, [endTime])

  if (
    (endTime <= currentTime || gameEndedForUser) &&
    gameStatus === GAME_STATUS.STARTED
  ) {
    // game has ended but did not receive the result from backend
    return <Loading label="Calculating result..." />
  }

  if (gameStatus === GAME_STATUS.ENDED) {
    return <Redirect href={`/game/${gameId}/result`} />
  }

  if (gameStatus !== GAME_STATUS.STARTED) {
    return <ErrorView errorMessage="Something went wrong" />
  }

  return <GroupGamePlay game={game} />
}

export default React.memo(GroupGamePlayContainer)
