import React, { useEffect, useMemo } from 'react';
import _size from 'lodash/size';
import PropTypes from 'prop-types';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import _includes from 'lodash/includes';
import _map from 'lodash/map';
import { Redirect } from 'expo-router';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import userReader from 'core/readers/userReader';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import { GAME_STATUS } from '../../constants/game';
import GameFullPage from '../GameFullPage/GameFullPage';
import useGameOwner from '../../hooks/useGameOwner';
import GroupPlayWaitingPage from '../GroupPlayWaitingPage';
import GroupGamePlay from '../GroupGamePlay';
import gameReader from '@/src/core/readers/gameReader';

const COMPONENT_FACTORY = {
  [GAME_STATUS.CREATED]: GroupPlayWaitingPage,
  [GAME_STATUS.READY]: GroupPlayWaitingPage,
  [GAME_STATUS.STARTED]: GroupGamePlay,
};

const JOINED_GAME_CALLED = {};

const GroupPlayPage = (props) => {
  const { game } = props;
  const { user } = useSession();
  const { joinGame } = useJoinGameQuery();

  const { players, gameStatus, _id: gameId, config } = game;

  const { checkIsGameOwner } = useGameOwner();
  const isGameOwner = checkIsGameOwner({ game });

  const isUserInTheGame = _includes(
    _map(players, 'userId'),
    userReader.id(user),
  );

  const numPlayers = gameReader.numPlayers(game);

  useEffect(() => {
    if (JOINED_GAME_CALLED[gameId]) return;

    JOINED_GAME_CALLED[gameId] = true;
    const shouldJoinTheGame =
      gameStatus === GAME_STATUS.CREATED &&
      _size(players) < numPlayers &&
      !isGameOwner;
    if (shouldJoinTheGame) {
      joinGame({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameStatus, players, isGameOwner, gameId, joinGame]);

  const Component = useMemo(() => COMPONENT_FACTORY[gameStatus], [gameStatus]);

  if (_size(players) === numPlayers && !isUserInTheGame) {
    return <GameFullPage />;
  }

  if (gameStatus === GAME_STATUS.ENDED) {
    return <Redirect href={`/game/${gameId}/result`} />;
  }

  if (gameStatus === GAME_STATUS.CREATED && _size(players) === 1) {
    if (isGameOwner) {
      return <GroupPlayWaitingPage game={game} />;
    }
    return <Loading label="Joining the Game..." />;
  }

  if (!Component) {
    return null;
  }

  if (!isUserInTheGame) {
    return <ErrorView errorMessage="You are not in the game" />;
  }

  return <Component game={game} gameId={gameId} />;
};

GroupPlayPage.propTypes = {
  game: PropTypes.object,
};

export default React.memo(GroupPlayPage);
