import _size from 'lodash/size';
import _map from 'lodash/map';
import _includes from 'lodash/includes';

import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { GAME_STATUS } from '../../constants/game';
import WaitingForUserToJoin from '../ChallengeUser/Components/WaitingForUserToJoin';
import UsersReadyToStartContainer from '../ChallengeUser/Components/UsersReadyToStart';
import GameResultPage from '../GameResultPage';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import GameFullPage from '../GameFullPage/GameFullPage';
import WaitingForFriend from '../PlayWithFriend/components/WaitingForFriend';
import FastestFingerPlayGame from './FastestFingerPlayGame';
import gameReader from '@/src/core/readers/gameReader';

const COMPONENT_FACTORY = {
  [GAME_STATUS.CREATED]: WaitingForUserToJoin,
  [GAME_STATUS.READY]: UsersReadyToStartContainer,
  [GAME_STATUS.STARTED]: FastestFingerPlayGame,
  [GAME_STATUS.ENDED]: GameResultPage,
};

const FastestFingerPlay = (props) => {
  const { game } = props;
  const { user, userId } = useSession();
  const { joinGame } = useJoinGameQuery();

  const { players, createdBy, gameStatus, _id: gameId, config } = game;

  const numPlayers = gameReader.numPlayers(game);

  useEffect(() => {
    if (
      gameStatus === GAME_STATUS.CREATED &&
      _size(players) === 1 &&
      user._id !== createdBy
    ) {
      joinGame({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameStatus, players, user]);

  if (_size(players) === 1 && userId === createdBy) {
    return <WaitingForFriend game={game} />;
  }

  if (
    _size(players) === numPlayers &&
    !_includes(_map(players, 'userId'), user._id)
  ) {
    return <GameFullPage />;
  }

  const Component = COMPONENT_FACTORY[gameStatus];

  if (!Component) {
    return null;
  }

  return <Component game={game} gameId={gameId} />;
};

FastestFingerPlay.propTypes = {
  game: PropTypes.object,
};

export default React.memo(FastestFingerPlay);
