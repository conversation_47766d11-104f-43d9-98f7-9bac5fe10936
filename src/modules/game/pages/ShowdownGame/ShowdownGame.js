import React, { useEffect } from 'react';
import _size from 'lodash/size';
import PropTypes from 'prop-types';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';

import _includes from 'lodash/includes';
import _map from 'lodash/map';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import dark from 'core/constants/themes/dark';
import { StyleSheet, View } from 'react-native';
import Loading from 'atoms/Loading';
import Header from 'shared/Header';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import { GAME_STATUS } from '../../constants/game';
import ShowdownGameLobbyPlayerCards from '../../components/ShowdownGameLobbyPlayerCards/ShowdownGameLobbyPlayerCards';
import ShowdownPlayGame from '../ShowdownPlayGame/ShowdownPlayGame';
import useShowdownQuery from '../../../showdown/hooks/useShowdownQuery';
import GameFullPage from '../GameFullPage/GameFullPage';
import gameReader from '@/src/core/readers/gameReader';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    // padding: 32,
  },
  contentContainer: { width: '100%', minHeight: 230 },
  leaveGameStyle: {
    color: dark.colors.textDark,
    fontSize: 16,
    marginTop: 16,
    marginBottom: 16,
    fontFamily: 'Montserrat-500',
  },
});

const ShowdownLobby = (props) => {
  const { game, showdown, loading } = props;

  if (loading) {
    return <Loading />;
  }

  return (
    <View style={{ flex: 1 }}>
      <Header title="Showdown Game" />
      <View style={styles.container}>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <ShowdownGameLobbyPlayerCards game={game} showdown={showdown} />
          </View>
        </View>
      </View>
    </View>
  );
};

ShowdownLobby.propTypes = {
  game: PropTypes.object.isRequired,
  showdown: PropTypes.object.isRequired,
  loading: PropTypes.bool.isRequired,
};

const COMPONENT_FACTORY = {
  [GAME_STATUS.CREATED]: ShowdownLobby,
  [GAME_STATUS.READY]: ShowdownLobby,
  [GAME_STATUS.STARTED]: ShowdownPlayGame,
  [GAME_STATUS.ENDED]: ShowdownPlayGame,
};

const ShowdownGame = (props) => {
  const { game } = props;
  const { user } = useSession();
  const { joinGame } = useJoinGameQuery();

  const {
    players,
    gameStatus,
    _id: gameId,
    showdownId,
    config,
  } = game ?? EMPTY_OBJECT;

  const numPlayers = gameReader.numPlayers(game);
  const { showdownDetails, loading } = useShowdownQuery({
    showdownId,
  });

  useEffect(() => {
    if (gameStatus === GAME_STATUS.CREATED) {
      joinGame({ gameId }).then(() => {
        Analytics.track(ANALYTICS_EVENTS.JOIN_GAME, { gameId });
      });
    }
  }, [gameId, gameStatus, joinGame, players, user]);

  if (
    _size(players) === numPlayers &&
    !_includes(_map(players, 'userId'), user._id)
  ) {
    // game is full, redirect the user to game is full page.
    return <GameFullPage />;
  }

  const Component = COMPONENT_FACTORY[gameStatus];

  if (!Component) {
    return null;
  }

  return <Component game={game} showdown={showdownDetails} loading={loading} />;
};

ShowdownGame.propTypes = {
  game: PropTypes.object.isRequired,
};

export default React.memo(ShowdownGame);
