import { gql, useQuery } from '@apollo/client';

import { GAME_FRAGMENT } from 'core/graphql/fragments/game';

import _map from 'lodash/map';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _isNil from 'lodash/isNil';
import _size from 'lodash/size';
import { useMemo } from 'react';
import { Platform } from 'react-native';
import { saveQuestions } from '@/src/wasm';
import { GAME_TYPES } from 'core/constants/gameTypes';
import useUserStore from 'store/useUserStore';
import gameReader from '@/src/core/readers/gameReader';

const FETCH_GAME_QUERY = gql`
  ${GAME_FRAGMENT}
  query GetGameById($gameId: ID) {
    game: getGameById(gameId: $gameId) {
      ...CoreGameFields
    }
  }
`;

const useFetchGameQuery = ({ gameId }: { gameId: string }) => {
  const { data, loading, error, refetch } = useQuery(FETCH_GAME_QUERY, {
    variables: {
      gameId,
    },
    fetchPolicy: 'cache-and-network',
  });

  const gameData = data?.game;
  // TODO : Add a Dynamic function to make question for different categories
  const { encryptedQuestions, ...coreGameDataFields } =
    gameData ?? EMPTY_OBJECT;

  const { isWasmReady } = useUserStore((state) => ({
    isWasmReady: state.isWasmReady,
  }));

  const questions = useMemo(() => {
    if (Platform.OS === 'web' && !isWasmReady) return [];
    if (_isNil(encryptedQuestions) || _size(encryptedQuestions) === 0)
      return [];
    if (
      gameReader.gameType(gameData) === GAME_TYPES.PLAY_ONLINE &&
      Platform.OS === 'web'
    ) {
      const _questions = saveQuestions(encryptedQuestions);
      return _questions;
    }
    return _map(encryptedQuestions, decryptJsonData);
  }, [gameData, isWasmReady, encryptedQuestions]);

  return {
    game: _isNil(gameData) ? gameData : { ...coreGameDataFields, questions },
    loading,
    error,
    reFetchGame: refetch,
  };
};

export default useFetchGameQuery;
