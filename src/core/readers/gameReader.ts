import _property from 'lodash/property';
import _get from 'lodash/get';
import { GAME_CATEGORY, GAME_MODES } from 'modules/game/constants/game';

const getTimeLimitOfGame = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameCategory = _get(gameObj, 'gameCategory');
  const categorySpecificGameConfig = _get(
    config,
    'categorySpecificConfig',
    EMPTY_OBJECT,
  );

  switch (gameCategory) {
    case GAME_CATEGORY.BLITZ:
      return _get(categorySpecificGameConfig, ['blitz', 'timeLimit']);
    case GAME_CATEGORY.CLASSICAL:
      return _get(categorySpecificGameConfig, ['classical', 'timeLimit']);
    case GAME_CATEGORY.MEMORY:
      return _get(categorySpecificGameConfig, ['memory', 'timeLimit']);
    case GAME_CATEGORY.PUZZLE:
      return _get(categorySpecificGameConfig, ['puzzle', 'timeLimit']);
    default:
      return _get(config, 'timeLimit');
  }
};

const getNumPlayersOfGame = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameMode = _get(gameObj, 'gameMode');
  const gameModeSpecificGameConfig = _get(
    config,
    'modeSpecificConfig',
    EMPTY_OBJECT,
  );

  switch (gameMode) {
    case GAME_MODES.ONLINE_SEARCH:
      return _get(gameModeSpecificGameConfig, ['onlineSearch', 'numPlayers']);
    case GAME_MODES.ONLINE_CHALLENGE:
      return _get(gameModeSpecificGameConfig, [
        'onlineChallenge',
        'numPlayers',
      ]);
    case GAME_MODES.GROUP_PLAY:
      return _get(gameModeSpecificGameConfig, ['groupPlay', 'maxPlayers']);
    case GAME_MODES.PRACTICE:
      return _get(gameModeSpecificGameConfig, ['practice', 'numPlayers']);
    case GAME_MODES.RUSH_WITH_TIME:
      return _get(gameModeSpecificGameConfig, ['rushWithTime', 'numPlayers']);
    case GAME_MODES.RUSH_WITHOUT_TIME:
      return _get(gameModeSpecificGameConfig, [
        'rushWithoutTime',
        'numPlayers',
      ]);
    case GAME_MODES.PLAY_VIA_LINK:
      return _get(gameModeSpecificGameConfig, ['playViaLink', 'numPlayers']);
    case GAME_MODES.SUMDAY_SHOWDOWN:
      return _get(gameModeSpecificGameConfig, ['sumdayShowdown', 'numPlayers']);
    case GAME_MODES.SURVIVAL_SATURDAY:
      return _get(gameModeSpecificGameConfig, [
        'survivalSaturday',
        'numPlayers',
      ]);
    default:
      return _get(config, 'numPlayers');
  }
};

const getGameModeSpecificConfig = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameMode = _get(gameObj, 'gameMode');
  const gameModeSpecificGameConfig = _get(
    gameObj,
    'modeSpecificConfig',
    EMPTY_OBJECT,
  );

  switch (gameMode) {
    case GAME_MODES.ONLINE_SEARCH:
      return _get(gameModeSpecificGameConfig, 'onlineSearch');
    case GAME_MODES.ONLINE_CHALLENGE:
      return _get(gameModeSpecificGameConfig, 'onlineChallenge');
    case GAME_MODES.GROUP_PLAY:
      return _get(gameModeSpecificGameConfig, 'groupPlay');
    case GAME_MODES.PRACTICE:
      return _get(gameModeSpecificGameConfig, 'practice');
    case GAME_MODES.RUSH_WITH_TIME:
      return _get(gameModeSpecificGameConfig, 'rushWithTime');
    case GAME_MODES.RUSH_WITHOUT_TIME:
      return _get(gameModeSpecificGameConfig, 'rushWithoutTime');
    case GAME_MODES.PLAY_VIA_LINK:
      return _get(gameModeSpecificGameConfig, 'playViaLink');
    case GAME_MODES.SUMDAY_SHOWDOWN:
      return _get(gameModeSpecificGameConfig, 'sumdayShowdown');
    case GAME_MODES.SURVIVAL_SATURDAY:
      return _get(gameModeSpecificGameConfig, 'survivalSaturday');
    default:
      return _get(config, 'config');
  }
};

export default {
  id: _property('_id'),
  createdBy: _property('createdBy'),
  gameStatus: _property('gameStatus'),
  config: getGameModeSpecificConfig,
  leaderBoard: _property('leaderBoard'),
  players: _property('players'),
  gameMode: _property('gameMode'),
  gameCategory: _property('gameCategory'),
  startTime: (gameObj: any) => _get(gameObj, 'startTime'),
  endTime: (gameObj: any) => _get(gameObj, 'endTime'),
  gameType: (gameObj: any) => _get(gameObj, 'gameType'),

  // Category Specific
  timeLimit: getTimeLimitOfGame,

  // Mode Specific
  numPlayers: getNumPlayersOfGame,
  modeSpecificConfig: getGameModeSpecificConfig,
};
