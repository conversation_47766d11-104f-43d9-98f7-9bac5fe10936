import React from 'react'; // Import hooks for state
import { QUESTION_CATEGORIES } from 'core/constants/questionCategories';
import PrimeFactorizationQuestion from 'shared/QuestionsRenderer/components/PrimeFactorizationQuestion';
import SumOfSquares from 'shared/QuestionsRenderer/components/SumOfSquares'; // Props interface
import MultiOperatorQuestion from 'shared/QuestionsRenderer/components/MultiOperatorQuestion';
import MCQWordProblemRenderer, {
  GenericQuestionType as MCQGenericQuestionType,
} from 'shared/MCQWordProblemQuestion/MCQWordProblemQuestion';
import { SecurityType } from 'core/constants/wasm';
import { Platform } from 'react-native';
import { GAME_TYPES } from 'core/constants/gameTypes';
import DMASQuestion from './components/DMASQuestion';
import NthRootQuestion from './components/NthRootQuestion';
import ExponentQuestion from './components/ExponentQuestion';
import FractionQuestion from './components/FractionQuestion';
import { QuestionType as OriginalGenericQuestionType } from './types';
import getQuestionCategoryFromPreset from './utils/getQeustionCategoryFromPreset';
import SecureCanvas from '../SecureCanvas';

type QuestionType = OriginalGenericQuestionType | MCQGenericQuestionType;

interface QuestionsRendererProps {
  question: QuestionType;
  inputProps: any;
  style: any;
  securityType: string;
  gameType: string;
}

const QuestionsRenderer: React.FC<QuestionsRendererProps> = (props) => {
  const { question, securityType, gameType } = props;
  const { category } = question;

  const qeustionCategory =
    category ??
    getQuestionCategoryFromPreset({
      presetIdentifier: question?.presetIdentifier,
    });

  switch (qeustionCategory) {
    case QUESTION_CATEGORIES.ROOT:
      return <NthRootQuestion {...props} />;
    case QUESTION_CATEGORIES.EXPONENT:
      return <ExponentQuestion {...props} />;
    case QUESTION_CATEGORIES.FRACTION:
      return <FractionQuestion {...props} />;
    case QUESTION_CATEGORIES.PRIME_FACTORIZATION:
      return <PrimeFactorizationQuestion {...props} />;
    case QUESTION_CATEGORIES.SUM_OF_SQUARES:
      return <SumOfSquares {...props} />;
    case QUESTION_CATEGORIES.MULTI_OPERATORS:
      return <MultiOperatorQuestion {...props} />;
    case QUESTION_CATEGORIES.MCQ_WORD_PROBLEM: {
      return (
        <MCQWordProblemRenderer
          question={props?.question as MCQGenericQuestionType}
          style={props?.style}
        />
      );
    }
    default:
      if (
        gameType &&
        securityType === SecurityType.WebSecured &&
        gameType === GAME_TYPES.PLAY_ONLINE &&
        Platform.OS === 'web'
      ) {
        return (
          <SecureCanvas
            width={400}
            height={400}
            questionId={question.id}
            {...props}
          />
        );
      }

      return <DMASQuestion {...props} />;
  }
};

export default React.memo(QuestionsRenderer);
